

# 🏗️ 技术方案：表格驱动的轻量低代码平台

## 1. 整体架构

```
[设计器] → [<PERSON><PERSON><PERSON>(JSON)] → [渲染引擎] → [后台页面]
     ↑                                   ↓
 [API配置]                          [客户差异配置]
```

* **设计器**：产品经理通过拖拽/配置生成 schema（表格、字段、表单）。
* **Schema(JSON)**：轻量 DSL，只包含必要信息（字段、布局、数据源、交互）。
* **渲染引擎**：React runtime，根据 schema 渲染后台页面。

---

## 2. Schema 设计（轻量 DSL）
u
### 页面级别

```json
{
  "layout": "single-column",
  "theme": "industryA",
  "components": [...]
}
```

### 组件级别（表格）

```json
{
  "type": "table",
  "props": {
    "columns": [
      {"title": "ID", "dataIndex": "id", "type": "text"},
      {"title": "用户名", "dataIndex": "username", "type": "text"},
      {"title": "邮箱", "dataIndex": "email", "type": "text"},
      {"title": "状态", "dataIndex": "status", "type": "select", "options": ["启用","禁用"]}
    ],
    "pagination": true
  },
  "dataBinding": {
    "source": "api_getUsers"
  }
}
```

### API 配置

```json
{
  "api_getUsers": {
    "url": "/api/users",
    "method": "GET",
    "params": ["username","status"],
    "responseMapping": "data.list"
  }
}
```

---

## 3. 渲染引擎设计

* **基于 React + Ant Design**（也可用 Arco Design 更轻量）
* **职责**：

  1. 解析 `layout` → 布局渲染
  2. 解析 `components` → 根据 `type` 渲染对应组件
  3. 执行 `dataBinding` → 发起 API 请求并绑定数据
  4. 应用 `actions` → 支持有限交互（侧拉、弹窗、刷新表格）
  5. 应用差异化 schema 合并

---

## 4. 部署模式

* **设计器**：部署在公司内部，供产品经理使用
* **产物**：`schema.json + runtime` → 可直接部署到客户内网
* **差异化**：客户 A、B、C 分别加载不同的差异 schema

---

## 6. 预期效果

* **产品经理提效**：只需要配置字段 + API，不用学复杂 DSL。
* **研发解放**：只维护 runtime，不需要频繁改页面。
* **差异需求低成本**：通过差异 schema 解决，不需要 fork 新页面。
* **维护可控**：schema 轻量，避免 amis 那种庞大配置。

---